/* 认证页面专用样式 */

/* 全局动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 自定义 CSS 变量 */
:root {
  --auth-primary-color: #3b82f6;
  --auth-primary-hover: #2563eb;
  --auth-secondary-color: #06b6d4;
  --auth-success-color: #10b981;
  --auth-error-color: #ef4444;
  --auth-warning-color: #f59e0b;
  
  --auth-bg-light: #f8fafc;
  --auth-bg-dark: #0f172a;
  --auth-surface-light: rgba(255, 255, 255, 0.95);
  --auth-surface-dark: rgba(30, 41, 59, 0.95);
  
  --auth-text-primary-light: #1e293b;
  --auth-text-primary-dark: #f1f5f9;
  --auth-text-secondary-light: #64748b;
  --auth-text-secondary-dark: #94a3b8;
  
  --auth-border-light: #e2e8f0;
  --auth-border-dark: #374151;
  
  --auth-shadow-light: rgba(0, 0, 0, 0.1);
  --auth-shadow-dark: rgba(0, 0, 0, 0.3);
  
  --auth-radius: 0.75rem;
  --auth-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色模式变量 */
[data-mantine-color-scheme="dark"] {
  --auth-bg: var(--auth-bg-dark);
  --auth-surface: var(--auth-surface-dark);
  --auth-text-primary: var(--auth-text-primary-dark);
  --auth-text-secondary: var(--auth-text-secondary-dark);
  --auth-border: var(--auth-border-dark);
  --auth-shadow: var(--auth-shadow-dark);
}

/* 浅色模式变量 */
[data-mantine-color-scheme="light"] {
  --auth-bg: var(--auth-bg-light);
  --auth-surface: var(--auth-surface-light);
  --auth-text-primary: var(--auth-text-primary-light);
  --auth-text-secondary: var(--auth-text-secondary-light);
  --auth-border: var(--auth-border-light);
  --auth-shadow: var(--auth-shadow-light);
}

/* 通用工具类 */
.auth-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.auth-slide-up {
  animation: slideUp 0.6s ease-out;
}

.auth-slide-down {
  animation: slideDown 0.6s ease-out;
}

.auth-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.auth-float {
  animation: float 6s ease-in-out infinite;
}

.auth-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* 玻璃态效果 */
.auth-glass {
  background: var(--auth-surface);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 渐变背景 */
.auth-gradient-bg {
  background: linear-gradient(135deg, var(--auth-bg) 0%, var(--auth-surface) 100%);
}

/* 按钮渐变效果 */
.auth-gradient-button {
  background: linear-gradient(135deg, var(--auth-primary-color) 0%, var(--auth-secondary-color) 100%);
  transition: var(--auth-transition);
}

.auth-gradient-button:hover {
  background: linear-gradient(135deg, var(--auth-primary-hover) 0%, var(--auth-primary-color) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* 输入框聚焦效果 */
.auth-input-focus {
  transition: var(--auth-transition);
}

.auth-input-focus:focus {
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 加载动画 */
.auth-loading {
  position: relative;
  overflow: hidden;
}

.auth-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -200px;
  width: 200px;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 2s infinite;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .auth-mobile-hidden {
    display: none;
  }
}

@media (min-width: 769px) {
  .auth-desktop-hidden {
    display: none;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .auth-glass {
    background: var(--auth-surface);
    backdrop-filter: none;
    border: 2px solid var(--auth-border);
  }
}
