/* 根容器 */
.root {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, 
    light-dark(#f8fafc, #0f172a) 0%, 
    light-dark(#e2e8f0, #1e293b) 100%);
  overflow: hidden;
}

/* 背景装饰 */
.backgroundDecoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.circle1,
.circle2,
.circle3 {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    light-dark(rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05)) 0%, 
    light-dark(rgba(147, 51, 234, 0.1), rgba(147, 51, 234, 0.05)) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle1 {
  width: 300px;
  height: 300px;
  top: -150px;
  right: -150px;
  animation-delay: 0s;
}

.circle2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  left: -100px;
  animation-delay: 2s;
}

.circle3 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: 10%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 容器 */
.container {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 480px;
  padding: 1rem;
}

/* 纸张容器 */
.paper {
  background: light-dark(
    rgba(255, 255, 255, 0.95),
    rgba(30, 41, 59, 0.95)
  );
  backdrop-filter: blur(10px);
  border: 1px solid light-dark(
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)
  );
  transition: all 0.3s ease;
  animation: slideUp 0.6s ease-out;
}

.paper:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px light-dark(
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.3)
  );
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo 容器 */
.logoContainer {
  margin-bottom: 1rem;
}

.logo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.5);
  }
}

/* 标题 */
.title {
  background: linear-gradient(135deg, 
    light-dark(#1e293b, #f1f5f9) 0%, 
    light-dark(#475569, #cbd5e1) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.subtitle {
  max-width: 300px;
  line-height: 1.5;
}

/* 表单容器 */
.formContainer {
  width: 100%;
  max-width: 320px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }
  
  .paper {
    padding: 1.5rem !important;
  }
  
  .circle1,
  .circle2,
  .circle3 {
    display: none;
  }
}

@media (max-width: 360px) {
  .formContainer {
    max-width: 280px;
  }
}
