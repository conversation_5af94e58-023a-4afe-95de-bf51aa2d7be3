"use client";

import {
  Box,
  Container,
  Paper,
  Stack,
  Text,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";
import classes from "./auth-layout.module.css";

interface AuthLayoutProps {
  children: ReactNode;
}

// 根据路径获取页面信息
function getPageInfo(pathname: string) {
  switch (pathname) {
    case "/login":
      return {
        title: "欢迎回来",
        subtitle: "请登录您的账户以继续使用我们的服务",
      };
    case "/register":
      return {
        title: "创建账户",
        subtitle: "加入我们，开始您的精彩旅程",
      };
    default:
      return {
        title: "认证",
        subtitle: "请完成身份验证",
      };
  }
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  const { colorScheme } = useMantineColorScheme();
  const pathname = usePathname();
  const { title, subtitle } = getPageInfo(pathname);

  return (
    <Box className={classes.root}>
      {/* 背景装饰 */}
      <div className={classes.backgroundDecoration}>
        <div className={classes.circle1} />
        <div className={classes.circle2} />
        <div className={classes.circle3} />
      </div>

      <Container size="sm" className={classes.container}>
        <Paper
          shadow="xl"
          p="xl"
          radius="lg"
          className={classes.paper}
          withBorder={colorScheme === "light"}
        >
          <Stack gap="lg" align="center">
            {/* Logo 区域 */}
            <Box className={classes.logoContainer}>
              <div className={classes.logo}>
                <Text
                  size="xl"
                  fw={700}
                  variant="gradient"
                  gradient={{ from: "blue", to: "cyan", deg: 45 }}
                >
                  KNOQ
                </Text>
              </div>
            </Box>

            {/* 标题区域 */}
            <Stack gap="xs" align="center">
              <Title order={2} ta="center" className={classes.title}>
                {title}
              </Title>
              {subtitle && (
                <Text c="dimmed" size="sm" ta="center" className={classes.subtitle}>
                  {subtitle}
                </Text>
              )}
            </Stack>

            {/* 表单内容 */}
            <Box className={classes.formContainer}>
              {children}
            </Box>
          </Stack>
        </Paper>
      </Container>
    </Box>
  );
}
