// src/components/UniversalAnsiPaster.tsx
import React, { useRef, useState } from "react";
import { UniversalAnsiPasterProps } from "../types/ansiTypes";
import AnsiParser from "./AnsiParser";

const UniversalAnsiPaster: React.FC<UniversalAnsiPasterProps> = ({
  initialContent = "",
}) => {
  const [ansiContent, setAnsiContent] = useState<string>(initialContent);
  const [showRaw, setShowRaw] = useState<boolean>(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handlePaste = (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    e.preventDefault();
    const text = e.clipboardData.getData("text/plain");
    setAnsiContent(text);
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setAnsiContent(e.target.value);
  };

  return (
    <div className="ansi-paster-container">
      <div className="header-section">
        <h1>通用 ANSI 解析器</h1>
        <p className="subtitle">解析任何来源的 ANSI 转义序列内容</p>
      </div>

      <div className="paste-section">
        <div className="input-group">
          <label htmlFor="ansi-input">粘贴或输入包含 ANSI 序列的内容:</label>
          <textarea
            id="ansi-input"
            ref={textareaRef}
            value={ansiContent}
            onChange={handleChange}
            onPaste={handlePaste}
            placeholder="从命令行、日志文件或其他来源复制内容后粘贴到这里..."
            className="paste-input"
            rows={8}
          />
        </div>

        <div className="controls">
          <button
            className="control-btn copy-btn"
            onClick={() => {
              if (textareaRef.current) {
                textareaRef.current.select();
                navigator.clipboard.writeText(textareaRef.current.value);
              }
            }}
          >
            <i className="icon-copy"></i> 复制原始内容
          </button>

          <button
            className="control-btn clear-btn"
            onClick={() => {
              setAnsiContent("");
              if (textareaRef.current) {
                textareaRef.current.value = "";
              }
            }}
          >
            <i className="icon-clear"></i> 清除内容
          </button>

          <label className="toggle-raw">
            <input
              type="checkbox"
              checked={showRaw}
              onChange={() => setShowRaw(!showRaw)}
            />
            显示原始内容
          </label>
        </div>
      </div>

      <div className="preview-section">
        <h2>解析结果预览</h2>
        <div className="preview-container">
          {ansiContent ? (
            <AnsiParser
              content={ansiContent}
              className="ansi-preview"
              preserveWhitespace={true}
            />
          ) : (
            <div className="empty-preview">
              <div className="empty-icon">
                <i className="icon-terminal"></i>
              </div>
              <p>内容将在这里显示</p>
              <p>请在上方粘贴或输入包含 ANSI 序列的内容</p>
            </div>
          )}
        </div>
      </div>

      {showRaw && ansiContent && (
        <div className="raw-section">
          <h2>原始内容</h2>
          <div className="raw-container">
            <pre className="raw-content">{ansiContent}</pre>
          </div>
        </div>
      )}

      <div className="info-section">
        <h3>支持的功能</h3>
        <ul className="features-list">
          <li>
            <i className="icon-check"></i> 解析所有常见 ANSI 转义序列
          </li>
          <li>
            <i className="icon-check"></i> 支持 16 色和 256 色显示
          </li>
          <li>
            <i className="icon-check"></i> 保留原始换行和空格
          </li>
          <li>
            <i className="icon-check"></i> 兼容 Windows、Linux 和 macOS
            命令行输出
          </li>
          <li>
            <i className="icon-check"></i> 支持来自任何来源的 ANSI 内容
          </li>
        </ul>
      </div>
    </div>
  );
};

export default UniversalAnsiPaster;
