// src/components/AnsiParser.tsx
import { AnsiParserProps, AnsiStyles } from "@/types/ansiTypes";
import React, { useMemo } from "react";

const AnsiParser: React.FC<AnsiParserProps> = ({
  content,
  preserveWhitespace = true,
  style,
  className,
}) => {
  const parsedContent = useMemo(() => {
    if (!content) return null;

    // ANSI 转义序列的正则表达式
    const ansiRegex = /(\x1b\[[0-9;]*m)/g;

    // 分割文本和 ANSI 序列
    const parts = content.split(ansiRegex).filter(Boolean);

    // 当前样式状态
    let currentStyles: AnsiStyles = {
      color: null,
      backgroundColor: null,
      fontWeight: null,
      fontStyle: null,
      textDecoration: null,
    };

    const elements: JSX.Element[] = [];
    let lineElements: JSX.Element[] = [];
    let currentLine: JSX.Element[] = [];
    let keyCounter = 0;

    const pushLine = () => {
      if (currentLine.length > 0) {
        lineElements.push(
          <div key={`line-${keyCounter}`} className="ansi-line">
            {currentLine}
          </div>
        );
        keyCounter++;
        currentLine = [];
      }
    };

    parts.forEach((part) => {
      // 如果是 ANSI 转义序列
      if (ansiRegex.test(part)) {
        // 解析 ANSI 代码
        const codes = part
          .replace(/\x1b\[/g, "")
          .replace(/m/g, "")
          .split(";")
          .filter((c) => c !== "")
          .map(Number);

        // 重置样式
        if (codes.includes(0)) {
          currentStyles = {
            color: null,
            backgroundColor: null,
            fontWeight: null,
            fontStyle: null,
            textDecoration: null,
          };
        }

        // 应用样式
        for (let i = 0; i < codes.length; i++) {
          const code = codes[i];

          // 文本颜色
          if (code === 30) currentStyles.color = "#000000";
          if (code === 31) currentStyles.color = "#ff0000";
          if (code === 32) currentStyles.color = "#00ff00";
          if (code === 33) currentStyles.color = "#ffff00";
          if (code === 34) currentStyles.color = "#0000ff";
          if (code === 35) currentStyles.color = "#ff00ff";
          if (code === 36) currentStyles.color = "#00ffff";
          if (code === 37) currentStyles.color = "#ffffff";

          // 背景颜色
          if (code === 40) currentStyles.backgroundColor = "#000000";
          if (code === 41) currentStyles.backgroundColor = "#ff0000";
          if (code === 42) currentStyles.backgroundColor = "#00ff00";
          if (code === 43) currentStyles.backgroundColor = "#ffff00";
          if (code === 44) currentStyles.backgroundColor = "#0000ff";
          if (code === 45) currentStyles.backgroundColor = "#ff00ff";
          if (code === 46) currentStyles.backgroundColor = "#00ffff";
          if (code === 47) currentStyles.backgroundColor = "#ffffff";

          // 文本样式
          if (code === 1) currentStyles.fontWeight = "bold";
          if (code === 3) currentStyles.fontStyle = "italic";
          if (code === 4) currentStyles.textDecoration = "underline";
          if (code === 7) {
            // 反色
            const temp = currentStyles.color;
            currentStyles.color = currentStyles.backgroundColor || "";
            currentStyles.backgroundColor = temp || "";
          }

          // 关闭样式
          if (code === 22) currentStyles.fontWeight = null;
          if (code === 23) currentStyles.fontStyle = null;
          if (code === 24) currentStyles.textDecoration = null;

          // 处理扩展颜色 (38;5;color 和 48;5;color)
          if (code === 38 && codes[i + 1] === 5) {
            currentStyles.color = get256Color(codes[i + 2]);
            i += 2;
          }
          if (code === 48 && codes[i + 1] === 5) {
            currentStyles.backgroundColor = get256Color(codes[i + 2]);
            i += 2;
          }
        }
      } else {
        // 文本内容
        const styleObj: React.CSSProperties = {
          color: currentStyles.color || undefined,
          backgroundColor: currentStyles.backgroundColor || undefined,
          fontWeight: currentStyles.fontWeight || undefined,
          fontStyle: currentStyles.fontStyle || undefined,
          textDecoration: currentStyles.textDecoration || undefined,
        };

        // 处理换行和空格
        if (preserveWhitespace) {
          const lines = part.split("\n");
          lines.forEach((line, lineIndex) => {
            if (lineIndex > 0) {
              pushLine();
            }

            // 添加当前行内容
            if (line) {
              currentLine.push(
                <span key={`span-${keyCounter}`} style={styleObj}>
                  {line}
                </span>
              );
              keyCounter++;
            }
          });
        } else {
          currentLine.push(
            <span key={`span-${keyCounter}`} style={styleObj}>
              {part}
            </span>
          );
          keyCounter++;
        }
      }
    });

    pushLine();
    return lineElements;
  }, [content, preserveWhitespace]);

  const containerStyle = preserveWhitespace
    ? { whiteSpace: "pre-wrap", ...style }
    : style;

  return (
    <div className={`ansi-parser ${className || ""}`} style={containerStyle}>
      {parsedContent}
    </div>
  );
};

// 256 色表映射
const get256Color = (code: number): string => {
  if (code < 16) {
    // 基本16色
    const baseColors = [
      "#000000",
      "#800000",
      "#008000",
      "#808000",
      "#000080",
      "#800080",
      "#008080",
      "#c0c0c0",
      "#808080",
      "#ff0000",
      "#00ff00",
      "#ffff00",
      "#0000ff",
      "#ff00ff",
      "#00ffff",
      "#ffffff",
    ];
    return baseColors[code];
  } else if (code < 232) {
    // 6x6x6 RGB 立方
    const r = Math.floor((code - 16) / 36);
    const g = Math.floor((code - 16 - r * 36) / 6);
    const b = (code - 16) % 6;

    const rgb = [
      r > 0 ? Math.floor(40 * r + 55) : 0,
      g > 0 ? Math.floor(40 * g + 55) : 0,
      b > 0 ? Math.floor(40 * b + 55) : 0,
    ];

    return `rgb(${rgb[0]}, ${rgb[1]}, ${rgb[2]})`;
  } else {
    // 灰度
    const gray = Math.floor(10 * (code - 232) + 8);
    return `rgb(${gray}, ${gray}, ${gray})`;
  }
};

export default AnsiParser;
