"use client";

import classes from "@/styles/auth/auth-layout.module.css";
import {
  Box,
  Container,
  Paper,
  Stack,
  Text,
  Title,
  useMantineColorScheme,
} from "@mantine/core";
import { ReactNode } from "react";

interface AuthLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
}

export function AuthLayout({ children, title, subtitle }: AuthLayoutProps) {
  const { colorScheme } = useMantineColorScheme();

  return (
    <Box className={classes.root}>
      {/* 背景装饰 */}
      <div className={classes.backgroundDecoration}>
        <div className={classes.circle1} />
        <div className={classes.circle2} />
        <div className={classes.circle3} />
      </div>

      <Container size="sm" className={classes.container}>
        <Paper
          shadow="xl"
          p="xl"
          radius="lg"
          className={classes.paper}
          withBorder={colorScheme === "light"}
        >
          <Stack gap="lg" align="center">
            {/* Logo 区域 */}
            <Box className={classes.logoContainer}>
              <div className={classes.logo}>
                <Text
                  size="xl"
                  fw={700}
                  variant="gradient"
                  gradient={{ from: "blue", to: "cyan", deg: 45 }}
                >
                  KNOQ
                </Text>
              </div>
            </Box>

            {/* 标题区域 */}
            <Stack gap="xs" align="center">
              <Title order={2} ta="center" className={classes.title}>
                {title}
              </Title>
              {subtitle && (
                <Text
                  c="dimmed"
                  size="sm"
                  ta="center"
                  className={classes.subtitle}
                >
                  {subtitle}
                </Text>
              )}
            </Stack>

            {/* 表单内容 */}
            <Box className={classes.formContainer}>{children}</Box>
          </Stack>
        </Paper>
      </Container>
    </Box>
  );
}
