/* 表单容器 */
.form {
  width: 100%;
}

/* 输入框样式 */
.input {
  transition: all 0.3s ease;
  border: 1px solid light-dark(#e2e8f0, #374151);
  background: light-dark(#ffffff, #1f2937);
}

.input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.input:hover:not(:focus) {
  border-color: light-dark(#cbd5e1, #4b5563);
}

/* 标签样式 */
.label {
  font-weight: 500;
  color: light-dark(#374151, #d1d5db);
  margin-bottom: 0.5rem;
}

/* 链接样式 */
.link {
  text-decoration: none;
  transition: all 0.2s ease;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
  transform: translateY(-1px);
}

/* 提交按钮样式 */
.submitButton {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  transition: all 0.3s ease;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.submitButton:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.submitButton:active {
  transform: translateY(0);
}

/* 密码强度指示器 */
.passwordStrength {
  margin-top: 0.5rem;
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .form {
    padding: 0;
  }
}

/* 深色模式特定样式 */
@media (prefers-color-scheme: dark) {
  .input {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }
  
  .label {
    color: #d1d5db;
  }
}

/* 表单验证状态 */
.input[data-invalid="true"] {
  border-color: #ef4444;
}

.input[data-invalid="true"]:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 成功状态 */
.input[data-valid="true"] {
  border-color: #10b981;
}

.input[data-valid="true"]:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* 密码强度颜色 */
.passwordStrength [data-progress-color="red"] {
  background-color: #ef4444;
}

.passwordStrength [data-progress-color="yellow"] {
  background-color: #f59e0b;
}

.passwordStrength [data-progress-color="blue"] {
  background-color: #3b82f6;
}

.passwordStrength [data-progress-color="green"] {
  background-color: #10b981;
}

/* 复选框样式增强 */
.form input[type="checkbox"] {
  accent-color: #3b82f6;
}

/* 聚焦环增强 */
.form *:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 加载状态 */
.submitButton[data-loading="true"] {
  pointer-events: none;
  opacity: 0.8;
}
