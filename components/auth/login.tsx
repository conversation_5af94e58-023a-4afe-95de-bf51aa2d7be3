"use client";

import classes from "@/styles/auth/login.module.css";
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Checkbox,
  Divider,
  Group,
  PasswordInput,
  Stack,
  Text,
  TextInput,
  Transition,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { Eye, EyeOff, Lock, Mail } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

const VisibilityToggleIcon = ({ reveal }: { reveal: boolean }) =>
  reveal ? (
    <EyeOff
      style={{ width: "var(--psi-icon-size)", height: "var(--psi-icon-size)" }}
    />
  ) : (
    <Eye
      style={{ width: "var(--psi-icon-size)", height: "var(--psi-icon-size)" }}
    />
  );

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

function Login() {
  const [loading, setLoading] = useState(false);
  const [mounted, { open }] = useDisclosure(false);

  const form = useForm<LoginFormData>({
    mode: "uncontrolled",
    initialValues: {
      email: "",
      password: "",
      rememberMe: false,
    },

    validate: {
      email: (value) => {
        if (!value) return "邮箱不能为空";
        if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
          return "请输入有效的邮箱地址";
        }
        return null;
      },
      password: (value) => {
        if (!value) return "密码不能为空";
        if (value.length < 6) return "密码至少需要6个字符";
        return null;
      },
    },
  });

  const handleSubmit = async (values: LoginFormData) => {
    setLoading(true);

    try {
      // 模拟登录请求
      await new Promise((resolve) => setTimeout(resolve, 2000));

      notifications.show({
        title: "登录成功",
        message: "欢迎回来！",
        color: "green",
      });

      console.log("登录数据:", values);
    } catch (error) {
      notifications.show({
        title: "登录失败",
        message: "邮箱或密码错误，请重试",
        color: "red",
      });
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载后触发动画
  useEffect(() => {
    const timer = setTimeout(open, 100);
    return () => clearTimeout(timer);
  }, [open]);

  return (
    <Transition
      mounted={mounted}
      transition="slide-up"
      duration={400}
      timingFunction="ease"
    >
      {(styles) => (
        <form
          onSubmit={form.onSubmit(handleSubmit)}
          style={styles}
          className={classes.form}
        >
          <Stack gap="md">
            {/* 邮箱输入 */}
            <TextInput
              label="邮箱地址"
              placeholder="请输入您的邮箱"
              leftSection={<Mail size={16} />}
              size="md"
              radius="md"
              classNames={{
                input: classes.input,
                label: classes.label,
              }}
              key={form.key("email")}
              {...form.getInputProps("email")}
            />

            {/* 密码输入 */}
            <PasswordInput
              label="密码"
              placeholder="请输入您的密码"
              leftSection={<Lock size={16} />}
              size="md"
              radius="md"
              visibilityToggleIcon={VisibilityToggleIcon}
              classNames={{
                input: classes.input,
                label: classes.label,
              }}
              key={form.key("password")}
              {...form.getInputProps("password")}
            />

            {/* 记住我和忘记密码 */}
            <Group justify="space-between" mt="xs">
              <Checkbox
                label="记住我"
                size="sm"
                key={form.key("rememberMe")}
                {...form.getInputProps("rememberMe", { type: "checkbox" })}
              />
              <Anchor size="sm" c="blue" className={classes.link}>
                忘记密码？
              </Anchor>
            </Group>

            {/* 登录按钮 */}
            <Button
              type="submit"
              size="md"
              radius="md"
              loading={loading}
              className={classes.submitButton}
              fullWidth
            >
              {loading ? "登录中..." : "登录"}
            </Button>

            {/* 分割线 */}
            <Divider label="或者" labelPosition="center" my="md" />

            {/* 注册链接 */}
            <Text ta="center" size="sm" c="dimmed">
              还没有账户？{" "}
              <Link href="/register" passHref>
                <Anchor size="sm" c="blue" className={classes.link}>
                  立即注册
                </Anchor>
              </Link>
            </Text>
          </Stack>
        </form>
      )}
    </Transition>
  );
}

export default Login;
