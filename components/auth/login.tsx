import { Button, Group, PasswordInput, TextInput } from "@mantine/core";
import { useForm } from "@mantine/form";
import { Eye, EyeOff } from "lucide-react";

const VisibilityToggleIcon = ({ reveal }: { reveal: boolean }) =>
  reveal ? (
    <EyeOff
      style={{ width: "var(--psi-icon-size)", height: "var(--psi-icon-size)" }}
    />
  ) : (
    <Eye
      style={{ width: "var(--psi-icon-size)", height: "var(--psi-icon-size)" }}
    />
  );

function Login() {
  const form = useForm({
    mode: "uncontrolled",
    initialValues: {
      email: "",
      termsOfService: false,
    },

    validate: {
      email: (value) =>
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)
          ? null
          : "Invalid email",
    },
  });

  return (
    <form onSubmit={form.onSubmit((values) => console.log(values))}>
      <TextInput
        maw={320}
        mx="auto"
        withAsterisk
        label="邮箱"
        placeholder="<EMAIL>"
        key={form.key("email")}
        {...form.getInputProps("email")}
      />

      <PasswordInput
        maw={320}
        mx="auto"
        label="密码"
        placeholder="请输入密码"
        defaultValue=""
        visibilityToggleIcon={VisibilityToggleIcon}
      />

      <Group justify="flex-end" mt="md">
        <Button type="submit">Submit</Button>
      </Group>
    </form>
  );
}

export default Login;
