"use client";

import classes from "@/styles/auth/register.module.css";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Checkbox,
  Divider,
  Group,
  PasswordInput,
  Progress,
  Stack,
  Text,
  TextInput,
  Transition,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { Eye, EyeOff, Lock, Mail } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

const VisibilityToggleIcon = ({ reveal }: { reveal: boolean }) =>
  reveal ? (
    <EyeOff
      style={{ width: "var(--psi-icon-size)", height: "var(--psi-icon-size)" }}
    />
  ) : (
    <Eye
      style={{ width: "var(--psi-icon-size)", height: "var(--psi-icon-size)" }}
    />
  );

interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  agreeToTerms: boolean;
}

// 密码强度检查函数
function getPasswordStrength(password: string): {
  score: number;
  label: string;
  color: string;
} {
  let score = 0;

  if (password.length >= 8) score += 25;
  if (password.length >= 12) score += 25;
  if (/[a-z]/.test(password)) score += 12.5;
  if (/[A-Z]/.test(password)) score += 12.5;
  if (/[0-9]/.test(password)) score += 12.5;
  if (/[^A-Za-z0-9]/.test(password)) score += 12.5;

  if (score < 30) return { score, label: "弱", color: "red" };
  if (score < 60) return { score, label: "中等", color: "yellow" };
  if (score < 90) return { score, label: "强", color: "blue" };
  return { score, label: "很强", color: "green" };
}

function Signup() {
  const [loading, setLoading] = useState(false);
  const [mounted, { open }] = useDisclosure(false);

  const form = useForm<RegisterFormData>({
    mode: "uncontrolled",
    initialValues: {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
    },

    validate: {
      username: (value) => {
        if (!value) return "用户名不能为空";
        if (value.length < 3) return "用户名至少需要3个字符";
        if (value.length > 20) return "用户名不能超过20个字符";
        if (!/^[a-zA-Z0-9_]+$/.test(value))
          return "用户名只能包含字母、数字和下划线";
        return null;
      },
      email: (value) => {
        if (!value) return "邮箱不能为空";
        if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)) {
          return "请输入有效的邮箱地址";
        }
        return null;
      },
      password: (value) => {
        if (!value) return "密码不能为空";
        if (value.length < 8) return "密码至少需要8个字符";
        const strength = getPasswordStrength(value);
        if (strength.score < 30) return "密码强度太弱，请使用更复杂的密码";
        return null;
      },
      confirmPassword: (value, values) => {
        if (!value) return "请确认密码";
        if (value !== values.password) return "两次输入的密码不一致";
        return null;
      },
      agreeToTerms: (value) => {
        if (!value) return "请同意用户协议和隐私政策";
        return null;
      },
    },
  });

  const handleSubmit = async (values: RegisterFormData) => {
    setLoading(true);

    try {
      // 模拟注册请求
      await new Promise((resolve) => setTimeout(resolve, 2000));

      notifications.show({
        title: "注册成功",
        message: "欢迎加入我们！请查看邮箱完成验证。",
        color: "green",
      });

      console.log("注册数据:", values);
    } catch (error) {
      console.log(error);
      notifications.show({
        title: "注册失败",
        message: "注册过程中出现错误，请重试",
        color: "red",
      });
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载后触发动画
  useEffect(() => {
    const timer = setTimeout(open, 100);
    return () => clearTimeout(timer);
  }, [open]);

  // 获取密码强度
  const passwordValue = form.getValues().password;
  const passwordStrength = passwordValue
    ? getPasswordStrength(passwordValue)
    : null;

  return (
    <Transition
      mounted={mounted}
      transition="slide-up"
      duration={400}
      timingFunction="ease"
    >
      {(styles) => (
        <form
          onSubmit={form.onSubmit(handleSubmit)}
          style={styles}
          className={classes.form}
        >
          <Stack gap="md">
            {/* 邮箱输入 */}
            <TextInput
              label="邮箱地址"
              placeholder="请输入您的邮箱"
              leftSection={<Mail size={16} />}
              size="md"
              radius="md"
              classNames={{
                input: classes.input,
                label: classes.label,
              }}
              key={form.key("email")}
              {...form.getInputProps("email")}
            />

            {/* 密码输入 */}
            <div>
              <PasswordInput
                label="密码"
                placeholder="请输入密码"
                leftSection={<Lock size={16} />}
                size="md"
                radius="md"
                visibilityToggleIcon={VisibilityToggleIcon}
                classNames={{
                  input: classes.input,
                  label: classes.label,
                }}
                key={form.key("password")}
                {...form.getInputProps("password")}
              />

              {/* 密码强度指示器 */}
              {passwordStrength && (
                <div className={classes.passwordStrength}>
                  <Group justify="space-between" mt="xs" mb={5}>
                    <Text size="xs" c="dimmed">
                      密码强度
                    </Text>
                    <Text size="xs" c={passwordStrength.color}>
                      {passwordStrength.label}
                    </Text>
                  </Group>
                  <Progress
                    value={passwordStrength.score}
                    color={passwordStrength.color}
                    size="xs"
                    radius="xl"
                  />
                </div>
              )}
            </div>

            {/* 确认密码输入 */}
            <PasswordInput
              label="确认密码"
              placeholder="请再次输入密码"
              leftSection={<Lock size={16} />}
              size="md"
              radius="md"
              visibilityToggleIcon={VisibilityToggleIcon}
              classNames={{
                input: classes.input,
                label: classes.label,
              }}
              key={form.key("confirmPassword")}
              {...form.getInputProps("confirmPassword")}
            />

            {/* 用户协议 */}
            <Checkbox
              label={
                <Text size="sm">
                  我同意{" "}
                  <Anchor size="sm" c="blue" className={classes.link}>
                    用户协议
                  </Anchor>{" "}
                  和{" "}
                  <Anchor size="sm" c="blue" className={classes.link}>
                    隐私政策
                  </Anchor>
                </Text>
              }
              size="sm"
              key={form.key("agreeToTerms")}
              {...form.getInputProps("agreeToTerms", { type: "checkbox" })}
            />

            {/* 注册按钮 */}
            <Button
              type="submit"
              size="md"
              radius="md"
              loading={loading}
              className={classes.submitButton}
              fullWidth
            >
              {loading ? "注册中..." : "创建账户"}
            </Button>

            {/* 分割线 */}
            <Divider label="或者" labelPosition="center" my="md" />

            {/* 登录链接 */}
            <Text ta="center" size="sm" c="dimmed">
              已有账户？{" "}
              <Link href="/login" passHref>
                <Anchor size="sm" c="blue" className={classes.link}>
                  立即登录
                </Anchor>
              </Link>
            </Text>
          </Stack>
        </form>
      )}
    </Transition>
  );
}

export default Signup;
