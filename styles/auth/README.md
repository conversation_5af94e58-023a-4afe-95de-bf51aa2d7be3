# 认证模块样式文件

这个目录包含了所有认证相关的样式文件，统一管理登录、注册等认证功能的样式。

## 文件结构

```
styles/auth/
├── README.md                    # 说明文档
├── index.css                    # 样式索引文件，包含全局变量和工具类
├── auth-layout.module.css       # 认证布局样式（共享布局）
├── login.module.css            # 登录表单样式
└── register.module.css         # 注册表单样式
```

## 样式文件说明

### `index.css`
- 包含认证模块的全局 CSS 变量
- 定义通用的工具类
- 支持深色/浅色模式切换
- 包含响应式设计和无障碍支持

### `auth-layout.module.css`
- 认证页面的共享布局样式
- 包含背景装饰、容器、卡片等样式
- 动画效果和响应式设计
- 被 `app/(auth)/layout.tsx` 使用

### `login.module.css`
- 登录表单的专用样式
- 输入框、按钮、链接等组件样式
- 表单验证状态样式
- 被 `components/auth/login.tsx` 使用

### `register.module.css`
- 注册表单的专用样式
- 包含密码强度指示器样式
- 表单验证和状态样式
- 被 `components/auth/register.tsx` 使用

## 使用方式

### 在组件中引用样式

```tsx
// 引用特定的模块样式
import classes from "@/styles/auth/login.module.css";

// 在组件中使用
<form className={classes.form}>
  <input className={classes.input} />
  <button className={classes.submitButton}>提交</button>
</form>
```

### 使用全局变量

```css
/* 在任何 CSS 文件中使用认证模块的变量 */
.my-auth-component {
  background: var(--auth-primary);
  border-radius: var(--auth-radius);
  transition: var(--auth-transition);
}
```

## 设计原则

1. **模块化**: 每个功能模块有独立的样式文件
2. **可复用**: 通过 CSS 变量和工具类提高复用性
3. **响应式**: 支持各种屏幕尺寸
4. **主题化**: 支持深色/浅色模式切换
5. **无障碍**: 遵循无障碍设计原则
6. **性能**: 使用 CSS 模块避免样式冲突

## 颜色系统

### 主要颜色
- `--auth-primary`: #3b82f6 (蓝色)
- `--auth-secondary`: #06b6d4 (青色)
- `--auth-success`: #10b981 (绿色)
- `--auth-error`: #ef4444 (红色)
- `--auth-warning`: #f59e0b (黄色)

### 背景颜色
- 浅色模式: #f8fafc
- 深色模式: #0f172a

### 文本颜色
- 主要文本: 根据主题自动切换
- 次要文本: 较低对比度的灰色

## 动画效果

- `auth-fade-in`: 淡入动画
- `auth-slide-up`: 上滑动画
- `auth-float`: 浮动动画（用于背景装饰）

## 响应式断点

- 移动设备: < 768px
- 平板设备: 768px - 1024px
- 桌面设备: > 1024px

## 维护指南

1. 新增认证相关样式时，应该放在对应的模块文件中
2. 通用样式和变量应该添加到 `index.css`
3. 保持样式的一致性，使用统一的变量和工具类
4. 测试深色/浅色模式的兼容性
5. 确保响应式设计在各种设备上正常工作
