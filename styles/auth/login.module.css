/* 表单容器 */
.form {
  width: 100%;
}

/* 输入框样式 */
.input {
  transition: all 0.3s ease;
  border: 1px solid light-dark(#e2e8f0, #374151);
  background: light-dark(#ffffff, #1f2937);
}

.input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.input:hover:not(:focus) {
  border-color: light-dark(#cbd5e1, #4b5563);
}

/* 标签样式 */
.label {
  font-weight: 500;
  color: light-dark(#374151, #d1d5db);
  margin-bottom: 0.5rem;
}

/* 链接样式 */
.link {
  text-decoration: none;
  transition: all 0.2s ease;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
  transform: translateY(-1px);
}

/* 提交按钮样式 */
.submitButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  transition: all 0.3s ease;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.submitButton:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.submitButton:active {
  transform: translateY(0);
}

/* 动画效果 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .form {
    padding: 0;
  }
}

/* 深色模式特定样式 */
@media (prefers-color-scheme: dark) {
  .input {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }
  
  .label {
    color: #d1d5db;
  }
}
