/* 认证模块样式索引文件 */

/* 导入所有认证相关的样式 */
@import "./auth-layout.module.css";
@import "./login.module.css";
@import "./register.module.css";

/* 认证模块的全局样式变量 */
:root {
  /* 认证主题颜色 */
  --auth-primary: #3b82f6;
  --auth-primary-hover: #2563eb;
  --auth-secondary: #06b6d4;
  --auth-success: #10b981;
  --auth-error: #ef4444;
  --auth-warning: #f59e0b;
  
  /* 认证背景色 */
  --auth-bg-light: #f8fafc;
  --auth-bg-dark: #0f172a;
  --auth-surface-light: rgba(255, 255, 255, 0.95);
  --auth-surface-dark: rgba(30, 41, 59, 0.95);
  
  /* 认证文本颜色 */
  --auth-text-primary-light: #1e293b;
  --auth-text-primary-dark: #f1f5f9;
  --auth-text-secondary-light: #64748b;
  --auth-text-secondary-dark: #94a3b8;
  
  /* 认证边框颜色 */
  --auth-border-light: #e2e8f0;
  --auth-border-dark: #374151;
  
  /* 认证阴影 */
  --auth-shadow-light: rgba(0, 0, 0, 0.1);
  --auth-shadow-dark: rgba(0, 0, 0, 0.3);
  
  /* 认证圆角和过渡 */
  --auth-radius: 0.75rem;
  --auth-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色模式变量 */
[data-mantine-color-scheme="dark"] {
  --auth-bg: var(--auth-bg-dark);
  --auth-surface: var(--auth-surface-dark);
  --auth-text-primary: var(--auth-text-primary-dark);
  --auth-text-secondary: var(--auth-text-secondary-dark);
  --auth-border: var(--auth-border-dark);
  --auth-shadow: var(--auth-shadow-dark);
}

/* 浅色模式变量 */
[data-mantine-color-scheme="light"] {
  --auth-bg: var(--auth-bg-light);
  --auth-surface: var(--auth-surface-light);
  --auth-text-primary: var(--auth-text-primary-light);
  --auth-text-secondary: var(--auth-text-secondary-light);
  --auth-border: var(--auth-border-light);
  --auth-shadow: var(--auth-shadow-light);
}

/* 认证模块通用工具类 */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-card {
  background: var(--auth-surface);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--auth-radius);
  transition: var(--auth-transition);
}

.auth-input {
  transition: var(--auth-transition);
  border: 1px solid var(--auth-border);
  background: var(--auth-surface);
}

.auth-input:focus {
  border-color: var(--auth-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.auth-button {
  background: linear-gradient(135deg, var(--auth-primary) 0%, var(--auth-secondary) 100%);
  border: none;
  transition: var(--auth-transition);
  font-weight: 600;
  letter-spacing: 0.025em;
}

.auth-button:hover {
  background: linear-gradient(135deg, var(--auth-primary-hover) 0%, var(--auth-primary) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* 认证动画 */
@keyframes auth-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes auth-slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes auth-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-container {
    padding: 1rem;
  }
  
  .auth-card {
    width: 100%;
    max-width: 400px;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .auth-card {
    background: var(--auth-surface);
    backdrop-filter: none;
    border: 2px solid var(--auth-border);
  }
  
  .auth-input {
    border-width: 2px;
  }
}
