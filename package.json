{"name": "knoq", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mantine/carousel": "^8.2.4", "@mantine/charts": "^8.2.4", "@mantine/code-highlight": "^8.2.4", "@mantine/core": "^8.2.4", "@mantine/dates": "^8.2.4", "@mantine/dropzone": "^8.2.4", "@mantine/form": "^8.2.4", "@mantine/hooks": "^8.2.4", "@mantine/modals": "^8.2.4", "@mantine/notifications": "^8.2.4", "@mantine/nprogress": "^8.2.4", "@mantine/spotlight": "^8.2.4", "@mantine/tiptap": "^8.2.4", "@tiptap/extension-link": "^3.0.9", "@tiptap/pm": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "dayjs": "^1.11.13", "embla-carousel": "^8.6.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.539.0", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.6", "postcss": "^8.5.6", "postcss-custom-properties": "^14.0.6", "postcss-mixins": "^12.1.2", "postcss-nested": "^7.0.2", "postcss-preset-mantine": "^1.18.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^4", "typescript": "^5"}}