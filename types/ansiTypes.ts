// src/types/ansiTypes.ts
export type AnsiStyles = {
  color?: string | null;
  backgroundColor?: string | null;
  fontWeight?: string | null;
  fontStyle?: string | null;
  textDecoration?: string | null;
};

export type AnsiParserProps = {
  content: string;
  preserveWhitespace?: boolean;
  style?: React.CSSProperties;
  className?: string;
};

export type UniversalAnsiPasterProps = {
  initialContent?: string;
};
